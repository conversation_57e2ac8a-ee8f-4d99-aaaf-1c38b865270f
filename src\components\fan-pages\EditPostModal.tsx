"use client";

import { Fragment, useState, useRef, useEffect } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { XMarkIcon, PhotoIcon, VideoCameraIcon } from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { useDropzone } from "react-dropzone";
import { uploadMultipleToCloudinary } from "@/lib/cloudinary";
import { toast } from "react-hot-toast";

interface FanPagePost {
  id: string;
  content: string;
  images: string[] | null;
  videos: string[] | null;
  type: string;
  likeCount: number;
  commentCount: number;
  shareCount: number;
  viewCount: number;
  createdAt: string;
  fanPage: {
    id: string;
    name: string;
    username: string;
    profileImage: string | null;
    isVerified: boolean;
  };
}

interface EditPostModalProps {
  isOpen: boolean;
  onClose: () => void;
  onPostUpdated: (updatedPost: FanPagePost) => void;
  post: FanPagePost;
  pageName: string;
  pageImage?: string | null;
}

export function EditPostModal({
  isOpen,
  onClose,
  onPostUpdated,
  post,
  pageName,
  pageImage,
}: EditPostModalProps) {
  const [content, setContent] = useState(post.content || "");
  const [images, setImages] = useState<File[]>([]);
  const [videos, setVideos] = useState<File[]>([]);
  const [existingImages, setExistingImages] = useState<string[]>(post.images || []);
  const [existingVideos, setExistingVideos] = useState<string[]>(post.videos || []);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (isOpen) {
      setContent(post.content || "");
      setExistingImages(post.images || []);
      setExistingVideos(post.videos || []);
      setImages([]);
      setVideos([]);
    }
  }, [isOpen, post]);

  const onImageDrop = (acceptedFiles: File[]) => {
    setImages(prev => [...prev, ...acceptedFiles]);
  };

  const onVideoDrop = (acceptedFiles: File[]) => {
    setVideos(prev => [...prev, ...acceptedFiles]);
  };

  const {
    getRootProps: getImageRootProps,
    getInputProps: getImageInputProps,
    isDragActive: isImageDragActive,
  } = useDropzone({
    onDrop: onImageDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp']
    },
    multiple: true,
  });

  const {
    getRootProps: getVideoRootProps,
    getInputProps: getVideoInputProps,
    isDragActive: isVideoDragActive,
  } = useDropzone({
    onDrop: onVideoDrop,
    accept: {
      'video/*': ['.mp4', '.mov', '.avi', '.mkv']
    },
    multiple: true,
  });

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
  };

  const removeVideo = (index: number) => {
    setVideos(prev => prev.filter((_, i) => i !== index));
  };

  const removeExistingImage = (index: number) => {
    setExistingImages(prev => prev.filter((_, i) => i !== index));
  };

  const removeExistingVideo = (index: number) => {
    setExistingVideos(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!content.trim() && images.length === 0 && videos.length === 0 && existingImages.length === 0 && existingVideos.length === 0) {
      toast.error("Please add some content, images, or videos");
      return;
    }

    setIsSubmitting(true);

    try {
      // Upload new images and videos to Cloudinary
      const newImageUrls: string[] = [];
      const newVideoUrls: string[] = [];

      if (images.length > 0) {
        const uploadedImageUrls = await uploadMultipleToCloudinary(images);
        newImageUrls.push(...uploadedImageUrls);
      }

      if (videos.length > 0) {
        const uploadedVideoUrls = await uploadMultipleToCloudinary(videos);
        newVideoUrls.push(...uploadedVideoUrls);
      }

      // Combine existing and new media
      const allImages = [...existingImages, ...newImageUrls];
      const allVideos = [...existingVideos, ...newVideoUrls];

      // Determine post type
      let postType = 'text';
      if (allImages.length > 0) postType = 'image';
      else if (allVideos.length > 0) postType = 'video';

      const response = await fetch(`/api/fan-pages/posts/${post.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: content.trim(),
          images: allImages,
          videos: allVideos,
          type: postType,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update post");
      }

      const data = await response.json();
      onPostUpdated(data.post);
      onClose();
      toast.success("Post updated successfully!");

      // Reset form
      setContent("");
      setImages([]);
      setVideos([]);
      setExistingImages([]);
      setExistingVideos([]);

    } catch (error) {
      console.error("Error updating post:", error);
      toast.error(error instanceof Error ? error.message : "Failed to update post");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-3xl transform overflow-hidden rounded-3xl bg-white text-left align-middle shadow-2xl transition-all border border-gray-100">
                {/* Header with gradient background */}
                <div className="bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-4 rounded-t-3xl">
                  <div className="flex items-center justify-between">
                    <Dialog.Title as="h3" className="text-xl font-semibold text-white flex items-center">
                      <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                      Edit Post
                    </Dialog.Title>
                    <button
                      onClick={onClose}
                      className="text-white/80 hover:text-white transition-colors p-2 rounded-full hover:bg-white/10"
                    >
                      <XMarkIcon className="h-6 w-6" />
                    </button>
                  </div>
                </div>

                {/* Main content with padding */}
                <div className="p-6">

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Page Info with enhanced styling */}
                  <div className="flex items-center space-x-4 p-4 bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl border border-gray-100">
                    <div className="h-14 w-14 rounded-full overflow-hidden ring-2 ring-white shadow-lg">
                      {pageImage ? (
                        <OptimizedImage
                          src={pageImage}
                          alt={pageName}
                          width={56}
                          height={56}
                          className="object-cover"
                        />
                      ) : (
                        <div className="h-full w-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                          <span className="text-xl font-bold text-white">
                            {pageName.charAt(0).toUpperCase()}
                          </span>
                        </div>
                      )}
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 text-lg">{pageName}</h4>
                      <p className="text-sm text-gray-600 flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        Editing post
                      </p>
                    </div>
                  </div>

                  {/* Content with enhanced styling */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Post Content
                    </label>
                    <div className="relative">
                      <textarea
                        ref={textareaRef}
                        value={content}
                        onChange={(e) => setContent(e.target.value)}
                        placeholder="What's on your mind? Share your thoughts..."
                        className="w-full p-4 border-2 border-gray-200 rounded-2xl resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white text-gray-800 placeholder-gray-500"
                        rows={5}
                      />
                      <div className="absolute bottom-3 right-3 text-xs text-gray-400">
                        {content.length} characters
                      </div>
                    </div>
                  </div>

                  {/* Existing Images with enhanced styling */}
                  {existingImages.length > 0 && (
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <h5 className="text-sm font-semibold text-gray-700">Current Images</h5>
                        <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">{existingImages.length}</span>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {existingImages.map((image, index) => (
                          <div key={index} className="relative group">
                            <OptimizedImage
                              src={image}
                              alt={`Existing image ${index + 1}`}
                              width={200}
                              height={200}
                              className="object-cover rounded-xl shadow-md group-hover:shadow-lg transition-shadow duration-200"
                            />
                            <button
                              type="button"
                              onClick={() => removeExistingImage(index)}
                              className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1.5 hover:bg-red-600 transition-colors duration-200 opacity-0 group-hover:opacity-100 shadow-lg"
                            >
                              <XMarkIcon className="h-4 w-4" />
                            </button>
                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 rounded-xl transition-all duration-200"></div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Existing Videos */}
                  {existingVideos.length > 0 && (
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">Current Videos</h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {existingVideos.map((video, index) => (
                          <div key={index} className="relative">
                            <video
                              src={video}
                              controls
                              className="w-full h-48 object-cover rounded-lg"
                            />
                            <button
                              type="button"
                              onClick={() => removeExistingVideo(index)}
                              className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                            >
                              <XMarkIcon className="h-4 w-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* New Images */}
                  {images.length > 0 && (
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">New Images</h5>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {images.map((image, index) => (
                          <div key={index} className="relative">
                            <img
                              src={URL.createObjectURL(image)}
                              alt={`New image ${index + 1}`}
                              className="w-full h-32 object-cover rounded-lg"
                            />
                            <button
                              type="button"
                              onClick={() => removeImage(index)}
                              className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                            >
                              <XMarkIcon className="h-4 w-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* New Videos */}
                  {videos.length > 0 && (
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">New Videos</h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {videos.map((video, index) => (
                          <div key={index} className="relative">
                            <video
                              src={URL.createObjectURL(video)}
                              controls
                              className="w-full h-48 object-cover rounded-lg"
                            />
                            <button
                              type="button"
                              onClick={() => removeVideo(index)}
                              className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                            >
                              <XMarkIcon className="h-4 w-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Enhanced Media Upload Buttons */}
                  <div className="space-y-4">
                    <h5 className="text-sm font-semibold text-gray-700 flex items-center">
                      <svg className="w-5 h-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      Add New Media
                    </h5>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div
                        {...getImageRootProps()}
                        className={`border-2 border-dashed rounded-2xl p-6 text-center cursor-pointer transition-all duration-300 ${
                          isImageDragActive
                            ? 'border-blue-400 bg-blue-50 scale-105 shadow-lg'
                            : 'border-gray-300 hover:border-blue-400 hover:bg-blue-50 hover:shadow-md'
                        }`}
                      >
                        <input {...getImageInputProps()} />
                        <div className="flex flex-col items-center space-y-2">
                          <PhotoIcon className={`h-10 w-10 mx-auto transition-colors ${
                            isImageDragActive ? 'text-blue-500' : 'text-gray-400'
                          }`} />
                          <p className="text-sm font-medium text-gray-700">
                            {isImageDragActive ? 'Drop images here' : 'Add Images'}
                          </p>
                          <p className="text-xs text-gray-500">
                            PNG, JPG, GIF up to 10MB
                          </p>
                        </div>
                      </div>

                      <div
                        {...getVideoRootProps()}
                        className={`border-2 border-dashed rounded-2xl p-6 text-center cursor-pointer transition-all duration-300 ${
                          isVideoDragActive
                            ? 'border-purple-400 bg-purple-50 scale-105 shadow-lg'
                            : 'border-gray-300 hover:border-purple-400 hover:bg-purple-50 hover:shadow-md'
                        }`}
                      >
                        <input {...getVideoInputProps()} />
                        <div className="flex flex-col items-center space-y-2">
                          <VideoCameraIcon className={`h-10 w-10 mx-auto transition-colors ${
                            isVideoDragActive ? 'text-purple-500' : 'text-gray-400'
                          }`} />
                          <p className="text-sm font-medium text-gray-700">
                            {isVideoDragActive ? 'Drop videos here' : 'Add Videos'}
                          </p>
                          <p className="text-xs text-gray-500">
                            MP4, MOV, AVI up to 100MB
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Enhanced Submit Buttons */}
                  <div className="flex justify-end space-x-3 pt-6 border-t border-gray-100">
                    <Button
                      type="button"
                      onClick={onClose}
                      variant="outline"
                      disabled={isSubmitting}
                      className="px-6 py-2.5 border-2 border-gray-300 hover:border-gray-400 text-gray-700 hover:text-gray-800 rounded-xl transition-all duration-200"
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={isSubmitting || (!content.trim() && images.length === 0 && videos.length === 0 && existingImages.length === 0 && existingVideos.length === 0)}
                      className="px-6 py-2.5 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? (
                        <div className="flex items-center space-x-2">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          <span>Updating...</span>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-2">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          <span>Update Post</span>
                        </div>
                      )}
                    </Button>
                  </div>
                </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
